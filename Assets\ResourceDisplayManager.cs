using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class ResourceDisplayManager : MonoBehaviour
{
    [Header("Resource Prefabs")]
    [SerializeField] private GameObject orePrefab;
    [SerializeField] private GameObject icePrefab;
    [SerializeField] private GameObject carbonPrefab;
    [SerializeField] private GameObject siliconPrefab;
    [SerializeField] private GameObject rareEarthsPrefab;
    [SerializeField] private GameObject alloysPrefab;
    [SerializeField] private GameObject fuelPrefab;
    [SerializeField] private GameObject graphenePrefab;
    [SerializeField] private GameObject ceramicsPrefab;
    [SerializeField] private GameObject microchipsPrefab;
    
    [Header("Layout Settings")]
    [SerializeField] private float resourceSpacing = 1f;
    [SerializeField] private float columnSpacing = 1f;
    [SerializeField] private int maxResourcesPerColumn = 5;
    
    private Dictionary<ResourceType, GameObject> resourcePrefabs = new Dictionary<ResourceType, GameObject>();
    
    // Dictionary to track resource instances by player, location, and type
    private Dictionary<int, Dictionary<GameObject, Dictionary<ResourceType, List<GameObject>>>> resourceInstances = 
        new Dictionary<int, Dictionary<GameObject, Dictionary<ResourceType, List<GameObject>>>>();
    
    private void Awake()
    {
        // Initialize resource prefabs dictionary
        resourcePrefabs[ResourceType.Ore] = orePrefab;
        resourcePrefabs[ResourceType.Ice] = icePrefab;
        resourcePrefabs[ResourceType.Carbon] = carbonPrefab;
        resourcePrefabs[ResourceType.Silicon] = siliconPrefab;
        resourcePrefabs[ResourceType.RareEarths] = rareEarthsPrefab;
        resourcePrefabs[ResourceType.Alloys] = alloysPrefab;
        resourcePrefabs[ResourceType.Fuel] = fuelPrefab;
        resourcePrefabs[ResourceType.Graphene] = graphenePrefab;
        resourcePrefabs[ResourceType.Ceramics] = ceramicsPrefab;
        resourcePrefabs[ResourceType.Microchips] = microchipsPrefab;
    }
    
    public void UpdatePlayerResourceDisplay(int playerId, GameObject location, Dictionary<ResourceType, int> resources)
    {
        // Initialize dictionaries if they don't exist
        if (!resourceInstances.ContainsKey(playerId))
        {
            resourceInstances[playerId] = new Dictionary<GameObject, Dictionary<ResourceType, List<GameObject>>>();
        }
        
        if (!resourceInstances[playerId].ContainsKey(location))
        {
            resourceInstances[playerId][location] = new Dictionary<ResourceType, List<GameObject>>();
        }
        
        // Get resource display area from player's play area
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager == null)
        {
            Debug.LogError("PlayAreaManager not found.");
            return;
        }

        PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(playerId);
        if (playerArea == null)
        {
            Debug.LogError($"PlayerPlayArea not found for player {playerId}.");
            return;
        }

        // Find the world card for this location
        GameObject worldCard = playerArea.GetWorldCard(location);
        if (worldCard == null)
        {
            Debug.LogError($"World card not found for location {location.name}.");
            return;
        }

        // For each resource type, update the display
        foreach (ResourceType resourceType in System.Enum.GetValues(typeof(ResourceType)))
        {
            // Skip non-physical resources like VP, Science, etc.
            if (resourceType == ResourceType.VP || resourceType == ResourceType.Science ||
                resourceType == ResourceType.Dollars || resourceType == ResourceType.Power ||
                resourceType == ResourceType.Strength || resourceType == ResourceType.Unknown)
                continue;

            int resourceCount = resources.ContainsKey(resourceType) ? resources[resourceType] : 0;

            // Create resource list if it doesn't exist
            if (!resourceInstances[playerId][location].ContainsKey(resourceType))
            {
                resourceInstances[playerId][location][resourceType] = new List<GameObject>();
            }

            List<GameObject> resourceObjects = resourceInstances[playerId][location][resourceType];

            // Add or remove resource objects as needed
            if (resourceCount > resourceObjects.Count)
            {
                // Add more resources
                int toAdd = resourceCount - resourceObjects.Count;
                for (int i = 0; i < toAdd; i++)
                {
                    AddResourceObject(playerId, location, resourceType, worldCard);
                }
            }
            else if (resourceCount < resourceObjects.Count)
            {
                // Remove excess resources
                int toRemove = resourceObjects.Count - resourceCount;
                for (int i = 0; i < toRemove; i++)
                {
                    RemoveResourceObject(playerId, location, resourceType);
                }
            }
        }
        
        // Position all resources after updates to ensure proper column arrangement
        PositionAllResources(playerId, location, worldCard);
    }

    // Modified to position all resources at once to ensure proper columns
    private void PositionAllResources(int playerId, GameObject location, GameObject worldCard)
    {
        if (!resourceInstances.ContainsKey(playerId) || !resourceInstances[playerId].ContainsKey(location))
            return;
            
        // Get column positions for each resource type
        Dictionary<ResourceType, int> columnOffsets = new Dictionary<ResourceType, int>();
        int currentColumn = 0;
        
        // Get the resources in a consistent order
        List<ResourceType> resourceOrder = new List<ResourceType>
        {
            ResourceType.Ore,
            ResourceType.Ice,
            ResourceType.Carbon,
            ResourceType.Silicon,
            ResourceType.RareEarths,
            ResourceType.Alloys,
            ResourceType.Fuel,
            ResourceType.Graphene,
            ResourceType.Ceramics,
            ResourceType.Microchips,
            ResourceType.Superconductors,
            ResourceType.MetallicHydrogen,
            ResourceType.Antimatter,
            ResourceType.Helium3
        };
        
        // First pass: calculate how many columns each resource type needs
        Dictionary<ResourceType, int> columnsPerResource = new Dictionary<ResourceType, int>();
        
        foreach (ResourceType resourceType in resourceOrder)
        {
            if (!resourceInstances[playerId][location].ContainsKey(resourceType) || 
                resourceInstances[playerId][location][resourceType].Count == 0)
                continue;
                
            int resourceCount = resourceInstances[playerId][location][resourceType].Count;
            int columns = Mathf.CeilToInt((float)resourceCount / maxResourcesPerColumn);
            columnsPerResource[resourceType] = columns;
            
            // Assign column offset for this resource type
            columnOffsets[resourceType] = currentColumn;
            currentColumn += columns;
        }
        
        // Second pass: position each resource based on its column offset
        foreach (ResourceType resourceType in resourceOrder)
        {
            if (!resourceInstances[playerId][location].ContainsKey(resourceType) || 
                resourceInstances[playerId][location][resourceType].Count == 0)
                continue;
                
            List<GameObject> resources = resourceInstances[playerId][location][resourceType];
            int columnOffset = columnOffsets[resourceType];
            
            for (int i = 0; i < resources.Count; i++)
            {
                GameObject resourceObj = resources[i];
                if (resourceObj == null) continue;
                
                int localColumn = i / maxResourcesPerColumn; // Which column within this resource type
                int row = i % maxResourcesPerColumn;         // Row within the column
                
                // Position relative to the world card's world position, organized in columns by resource type
                Vector3 worldCardPosition = worldCard.transform.position;
                resourceObj.transform.position = new Vector3(
                    worldCardPosition.x - 2.3f - ((columnOffset + localColumn) * columnSpacing),  // Right of the card, separated by type
                    worldCardPosition.y + .5f,                                                     // Slightly above the card
                    worldCardPosition.z - 1.9f + (row * resourceSpacing)                         // Stacked vertically
                );

                // Set consistent scale since resources are no longer children of world cards
                resourceObj.transform.localScale = Vector3.one * 0.95f;
            }
        }
    }

    private void AddResourceObject(int playerId, GameObject location, ResourceType resourceType, GameObject worldCard)
    {
        if (!resourcePrefabs.ContainsKey(resourceType) || resourcePrefabs[resourceType] == null)
            return;

        // Create the resource object as a sibling of the world card, not as a child
        GameObject resourceObj = Instantiate(resourcePrefabs[resourceType], worldCard.transform.parent);
        resourceObj.name = $"Resource_{resourceType}_{resourceInstances[playerId][location][resourceType].Count}";

        // Set consistent scale since resources are no longer children of world cards
        resourceObj.transform.localScale = Vector3.one * 0.95f;

        // Preserve aspect ratio for all resources
        Image resourceImage = resourceObj.GetComponent<Image>();
        if (resourceImage != null)
        {
            resourceImage.preserveAspect = true;
        }

        // Add to tracking list
        resourceInstances[playerId][location][resourceType].Add(resourceObj);
    }
    
    private void RemoveResourceObject(int playerId, GameObject location, ResourceType resourceType)
    {
        List<GameObject> resources = resourceInstances[playerId][location][resourceType];
        if (resources.Count == 0) return;
        
        GameObject resourceToRemove = resources[resources.Count - 1];
        resources.RemoveAt(resources.Count - 1);
        
        if (resourceToRemove != null)
            Destroy(resourceToRemove);
    }
    
    public int GetResourceColumnCount(int playerId, GameObject location)
    {
        if (!resourceInstances.ContainsKey(playerId) || 
            !resourceInstances[playerId].ContainsKey(location))
            return 0;
        
        int totalColumns = 0;
        foreach (var resourceList in resourceInstances[playerId][location])
        {
            int count = resourceList.Value.Count;
            if (count > 0)
            {
                // Calculate columns needed for this resource type
                totalColumns += Mathf.CeilToInt((float)count / maxResourcesPerColumn);
            }
        }
        
        return totalColumns;
    }

    public void ClearPlayerResourcesAtLocation(int playerId, GameObject location)
    {
        if (!resourceInstances.ContainsKey(playerId) ||
            !resourceInstances[playerId].ContainsKey(location))
            return;

        foreach (var resourceList in resourceInstances[playerId][location].Values)
        {
            foreach (GameObject resourceObj in resourceList)
            {
                if (resourceObj != null)
                    Destroy(resourceObj);
            }
            resourceList.Clear();
        }

        resourceInstances[playerId].Remove(location);
    }
}